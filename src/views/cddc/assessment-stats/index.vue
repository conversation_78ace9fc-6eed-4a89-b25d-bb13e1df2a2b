<template>
  <div class="assessment-stats-container">
    <BasicTablePlus @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-button type="link" @click="handleView(record)">查看详情</a-button>
        </template>
      </template>
    </BasicTablePlus>
  </div>
</template>

<script lang="ts" setup>
import { useTable } from '/@/components/BasicTablePlus/useTable';
import { columns } from './data';
import { V1ManageTrainStudyRecordsPagePost } from '/@/api/cddc.req';
import { ProjectType } from '/@/enums/projectEnum';
import { useRouter, useRoute } from 'vue-router';
import { onMounted, onUnmounted, ref } from 'vue';

const router = useRouter();
const route = useRoute();

// 控制是否应用URL参数的状态
const shouldApplyUrlParams = ref(true);
// 标记用户是否手动修改过项目字段
const userModifiedProject = ref(false);

const handleBeforeFetch = (params: any) => {
  const rawQuery = params.data || {};

  const data = {
    ...rawQuery,
    startTime: rawQuery.timeRange?.at(0),
    endTime: rawQuery.timeRange?.at(1),
    projectType: ProjectType.Exam,
  };

  // 如果从项目统计页面跳转过来且允许应用URL参数，且用户未手动修改过项目字段，添加项目筛选条件
  if (
    shouldApplyUrlParams.value &&
    !userModifiedProject.value &&
    route.query.projectId &&
    route.query.origin === 'project-stats'
  ) {
    data.projectIdList = [route.query.projectId];
  }

  params.data = data;
  return params;
};

const handleView = (record: any) => {
  console.log('View details:', record);
  router.push({
    path: '/cddc/report',
    query: { recordId: record.id, type: 2, origin: 'assessment-report' },
  });
};

const [registerTable, { getForm }] = useTable({
  columns,
  api: (params) => V1ManageTrainStudyRecordsPagePost(handleBeforeFetch(params)),
  beforeFetch: handleBeforeFetch,
  formConfig: {
    resetFunc: async () => {
      // 重置时禁用URL参数应用，避免重置后仍然保留从项目统计页面传入的筛选条件
      shouldApplyUrlParams.value = false;
      userModifiedProject.value = false;
    },
  },
});

// 页面加载时，如果有URL参数，自动设置搜索表单的值
onMounted(async () => {
  if (
    shouldApplyUrlParams.value &&
    route.query.projectId &&
    route.query.origin === 'project-stats'
  ) {
    // 延迟一下确保表单已经初始化
    setTimeout(() => {
      const form = getForm();
      form.setFieldsValue({
        projectIdList: [route.query.projectId],
      });
      // 触发搜索
      form.submit();
    }, 100);
  }

  // 设置全局回调函数，用于监听项目字段的变化
  (window as any).assessmentStatsProjectChanged = (data: any) => {
    // 当用户手动修改项目字段时，标记为已修改
    userModifiedProject.value = true;
  };
});

// 组件卸载时清理全局回调
onUnmounted(() => {
  delete (window as any).assessmentStatsProjectChanged;
});
</script>

<style lang="less" scoped>
.assessment-stats-container {
  padding: 0px 8px;
  background: #fff;
}
</style>

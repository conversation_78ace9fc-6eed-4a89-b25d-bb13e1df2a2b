import { V1ManageTrainStudyRecordsProjects, V1ManageCommonTrainStatusEnum } from '/@/api/cddc.req';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { formatSecondsToMinute } from '/@/utils/dateUtil';

export const columns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 160,
    search: {
      field: 'projectIdList',
      component: 'ApiSelect',
      colProps: {
        span: 6,
      },
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        mode: 'multiple',
        maxTagCount: 'responsive',
        optionFilterProp: 'label',
        api: async () => {
          // 获取所有项目，不限制项目类型，确保从项目统计页面跳转过来的项目能被找到
          const resp = await V1ManageTrainStudyRecordsProjects({projectType:2});
          return resp.map((item) => ({ ...item, label: item.name, value: item.id }));
        },
        onChange:(data: any)=>{
            // 通知父组件项目字段已被用户手动修改
            if ((window as any).assessmentStatsProjectChanged) {
              (window as any).assessmentStatsProjectChanged(data);
            }
        }
      },
    },
  },
  {
    title: '用户名',
    dataIndex: 'userName',
  },
  {
    title: '考核时长(min)',
    dataIndex: 'requestDuration',
  },
  {
    title: '要求次数',
    dataIndex: 'requestFrequency',
  },
  {
    title: '合格率要求',
    dataIndex: 'requestQualificationRate',
    customRender(opt) {
      return opt.value ? `${opt.value}%` : '0%';
    },
  },
  {
    title: '动作达标率要求',
    dataIndex: 'requestActionRate',
    width: 120,
    customRender(opt) {
      return opt.value ? `${opt.value}%` : '0%';
    },
  },
  {
    title: '实际考核时长',
    dataIndex: 'trainDurationSecond',
    customRender(opt) {
      return formatSecondsToMinute(opt.value! || 0);
    },
  },
  {
    title: '实际作业次数',
    dataIndex: 'opNum',
  },
  {
    title: '考核合格率',
    dataIndex: 'passRate',
    customRender(opt) {
      return opt.value ? `${opt.value}%` : '-';
    },
  },
  {
    title: '考核达标率',
    dataIndex: 'actionPassRate',
    customRender(opt) {
      return opt.value ? `${opt.value}%` : '-';
    },
  },
  {
    title: '考核状态',
    dataIndex: 'statusShow',
    search: {
      component: 'ApiSelect',
      colProps: {
        span: 6,
      },
      field: 'statusList',
      componentProps: {
        allowClear: true,
        showArrow: true,
        placeholder: '请选择',
        mode: 'multiple',
        maxTagCount: 'responsive',
        api: async () => {
          const resp = await V1ManageCommonTrainStatusEnum({ projectType: 2 });
          return resp.map((item) => ({ ...item, label: item.desc, value: item.code }));
        },
      },
    },
  },
];
